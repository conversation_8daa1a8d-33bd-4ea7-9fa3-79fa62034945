from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import padding


# 确保密钥长度是 16 字节
def ensure_key_length(key, desired_length=16):
    if len(key) > desired_length:
        return key[:desired_length]  # 截断多余的字节
    elif len(key) < desired_length:
        return key.ljust(desired_length, b'\0')  # 使用空字节填充密钥
    return key


# AES 加密
def aes_encrypt(message, key):
    backend = default_backend()
    key = ensure_key_length(key)
    cipher = Cipher(algorithms.AES(key), modes.CBC(key), backend=backend)
    encryptor = cipher.encryptor()
    padder = padding.PKCS7(128).padder()
    padded_data = padder.update(message.encode()) + padder.finalize()
    encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
    return encrypted_data.hex()


# AES 解密
def aes_decrypt(encrypted_hex, key):
    encrypted_data = bytes.fromhex(encrypted_hex)
    backend = default_backend()
    key = ensure_key_length(key)
    cipher = Cipher(algorithms.AES(key), modes.CBC(key), backend=backend)
    decryptor = cipher.decryptor()
    unpadder = padding.PKCS7(128).unpadder()
    decrypted_data = unpadder.update(decryptor.update(encrypted_data) + decryptor.finalize()) + unpadder.finalize()
    return decrypted_data.decode()


# 私钥加密
def private_key_encrypt(original_private_key, key):
    key_character = original_private_key[-1]
    key_character = chr(ord(key_character) + 1)
    waiting_encrypt_private_key = original_private_key[:-1] + key_character
    return aes_encrypt(waiting_encrypt_private_key, key)


# 私钥解密
def private_key_decrypt(encrypted_private_key, key):
    decrypted_private_key = aes_decrypt(encrypted_private_key, key)
    key_character = decrypted_private_key[-1]
    key_character = chr(ord(key_character) - 1)
    return decrypted_private_key[:-1] + key_character
